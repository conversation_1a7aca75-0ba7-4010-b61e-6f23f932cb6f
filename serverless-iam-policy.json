{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["cloudformation:*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["lambda:*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["apigateway:*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["iam:GetRole", "iam:PassRole", "iam:CreateRole", "iam:DeleteRole", "iam:DetachRolePolicy", "iam:PutRolePolicy", "iam:AttachRolePolicy", "iam:DeleteRolePolicy", "iam:GetRolePolicy", "iam:CreateServiceLinkedRole"], "Resource": "*"}, {"Effect": "Allow", "Action": ["s3:*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["logs:*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream", "bedrock:InvokeAgent"], "Resource": "*"}, {"Effect": "Allow", "Action": ["events:*"], "Resource": "*"}, {"Effect": "Allow", "Action": ["ssm:GetParameter", "ssm:PutParameter", "ssm:DeleteParameter", "ssm:GetParameters"], "Resource": "*"}]}