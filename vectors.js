const { Client } = require("pg");
require("dotenv").config();

class VectorizationService {
  constructor(dbConfig) {
    this.client = new Client(dbConfig);
  }

  async connect() {
    await this.client.connect();
  }

  async disconnect() {
    await this.client.end();
  }

  formatJSON(jsonData) {
    if (!jsonData) return "";
    if (typeof jsonData === "string") return jsonData;
    return JSON.stringify(jsonData);
  }

  formatArray(arrayData) {
    if (!arrayData || !Array.isArray(arrayData)) return "";
    return arrayData.join(", ");
  }

  cleanText(text) {
    if (!text) return "";
    return text.toString().replace(/\s+/g, " ").trim();
  }

  async vectorizeAddOns() {
    const query = `
      SELECT 
        a.add_on_id,
        a.add_on_name,
        a.add_on_type,
        a.add_on_price,
        a.fk_branch_id,
        b.branch_name,
        r.restaurant_name
      FROM "AddOns" a
      LEFT JOIN "Branches" b ON a.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      WHERE a.add_on_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `addon_${row.add_on_id}`,
      content: this.cleanText(`
        Add-on: ${row.add_on_name}
        Type: ${row.add_on_type}
        Price: ${row.add_on_price}
        Branch: ${row.branch_name || "N/A"}
        Restaurant: ${row.restaurant_name || "N/A"}
      `),
      metadata: {
        type: "addon",
        id: row.add_on_id,
        name: row.add_on_name,
        addon_type: row.add_on_type,
        price: row.add_on_price,
        branch_id: row.fk_branch_id,
        branch_name: row.branch_name,
        restaurant_name: row.restaurant_name,
      },
    }));
  }

  async vectorizeBranches() {
    const query = `
      SELECT 
        b.*,
        r.restaurant_name,
        r.phone_number resturant_whatsapp_number,
        r.take_orders
        r.is_only_for_listing
      FROM "Branches" b
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      WHERE b.branch_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `branch_${row.branch_id}`,
      content: this.cleanText(`
        Restaurant Branch Name: ${row.branch_name}
        Restaurant Name: ${row.restaurant_name || "N/A"}
        Address: ${row.branch_address}
        Order through Whatsapp (Link) : https://wa.me/${row.resturant_whatsapp_number}
        Description: ${row.branch_description || ""}
        Timings: ${this.formatJSON(row.branch_timings)}
        Tags: ${this.formatArray(row.branch_tags)}
        Emirate: ${row.branch_emirate || ""}
        Average Spend: ${row.average_spend || "N/A"}
        Payment Methods: ${this.formatArray(row.branch_payment_modes)}
        Delivery: ${row.branch_delivery ? "Available" : "Not Available"}
        Pickup: ${row.branch_pickup ? "Available" : "Not Available"}
        Taking Orders: ${row.take_orders ? "Yes" : "No"}
        This Restaurant Under Cravin(can order your favorite food through directly): ${row.is_only_for_listing ? "No" : "Yes"}

      `),
      metadata: {
        type: "branch",
        id: row.branch_id,
        name: row.branch_name,
        restaurant_id: row.fk_restaurant_id,
        restaurant_name: row.restaurant_name,
        address: row.branch_address,
        emirate: row.branch_emirate,
        status: row.status,
        delivery_available: row.branch_delivery,
        pickup_available: row.branch_pickup,
        takes_orders: row.take_orders,
        order_link_through_whatsapp: "https://wa.me/" + row.resturant_whatsapp_number,
        restaurant_under_cravin: !row.is_only_for_listing,
      },
    }));
  }

  async vectorizeCategories() {
    const query = `
      SELECT 
        c.*,
        b.branch_name,
        r.restaurant_name
      FROM "Categories" c
      LEFT JOIN "Branches" b ON c.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      WHERE c.category_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `category_${row.category_id}`,
      content: this.cleanText(`
        Food Category: ${row.category_name}
        Description: ${row.category_description || ""}
        Availability: ${row.category_availability}
        Branch: ${row.branch_name || "N/A"}
        Restaurant: ${row.restaurant_name || "N/A"}
        Timings: ${this.formatJSON(row.category_availability_timings)}
      `),
      metadata: {
        type: "category",
        id: row.category_id,
        name: row.category_name,
        branch_id: row.fk_branch_id,
        branch_name: row.branch_name,
        restaurant_name: row.restaurant_name,
        availability: row.category_availability,
        status: row.status,
      },
    }));
  }

  async vectorizeClubs() {
    const query = `SELECT * FROM "Clubs" WHERE club_name IS NOT NULL`;
    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `club_${row.club_id}`,
      content: this.cleanText(`
        Sports Club: ${row.club_name}
        Location: ${row.club_location || ""}
        Take Bookings(Enabled): ${row.take_booking ? "Yes" : "No"}
        Timings: ${this.formatJSON(row.club_timings)}
        Emirate: ${row.club_emirate || ""}
        Average Spend: ${row.average_spend || "N/A"}
        Tags: ${this.formatArray(row.club_tags)}
        Booking Policy: ${this.formatArray(row.booking_policy)}
        Payment Methods: ${this.formatJSON(row.payment_methods)}
        Book through Whatsapp(Link): https://wa.me/${row.phone_number}
      `),
      metadata: {
        type: "club",
        id: row.club_id,
        name: row.club_name,
        location: row.club_location,
        phone: row.phone_number,
        takes_booking: row.take_booking,
        emirate: row.club_emirate,
        subscription_status: row.subscription_status,
      },
    }));
  }

  async vectorizeCourts() {
    const query = `
      SELECT 
        c.*,
        f.facility_name,
        f.facility_category,
        f.facility_type,
        cl.club_name
      FROM "Courts" c
      LEFT JOIN "Facilities" f ON c.fk_facility_id = f.facility_id
      LEFT JOIN "Clubs" cl ON f.fk_club_id = cl.club_id
      WHERE c.court_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `court_${row.court_id}`,
      content: this.cleanText(`
        Court: ${row.court_name}
        Facility: ${row.facility_name || "N/A"}
        Category: ${row.facility_category || "N/A"}
        Type: ${row.facility_type || "N/A"}
        Club: ${row.club_name || "N/A"}
        Status: ${row.status ? "Active" : "Inactive"}
      `),
      metadata: {
        type: "court",
        id: row.court_id,
        name: row.court_name,
        facility_id: row.fk_facility_id,
        facility_name: row.facility_name,
        club_name: row.club_name,
        status: row.status,
      },
    }));
  }

  async vectorizeDeliveryZones() {
    const query = `
      SELECT 
        dz.*,
        b.branch_name,
        r.restaurant_name,
        s.shop_name
      FROM "DeliveryZones" dz
      LEFT JOIN "Branches" b ON dz.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON dz.fk_restaurant_id = r.restaurant_id
      LEFT JOIN "Shops" s ON dz.fk_shop_id = s.shop_id
      WHERE dz.zone_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `delivery_zone_${row.zone_id}`,
      content: this.cleanText(`
        Delivery Zone: ${row.zone_name}
        Branch: ${row.branch_name || "N/A"}
        Restaurant: ${row.restaurant_name || "N/A"}
        Shop: ${row.shop_name || "N/A"}
        Delivery Fee: ${row.delivery_fee || "N/A"}
        Minimum Cart Amount: ${row.min_cart_amount || "N/A"}
        Coordinates: ${this.formatJSON(row.coordinates)}
      `),
      metadata: {
        type: "restaurant_delivery_zone",
        id: row.zone_id,
        name: row.zone_name,
        branch_id: row.fk_branch_id,
        restaurant_id: row.fk_restaurant_id,
        shop_id: row.fk_shop_id,
        delivery_fee: row.delivery_fee,
        status: row.status,
      },
    }));
  }

  async vectorizeDiscounts() {
    const query = `
      SELECT 
        d.*,
        c.club_name
      FROM "Discounts" d
      LEFT JOIN "Clubs" c ON d.fk_club_id = c.club_id
      WHERE d.discount_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `discount_${row.discount_id}`,
      content: this.cleanText(`
        Discount: ${row.discount_name}
        Code: ${row.discount_code}
        Club: ${row.club_name || "N/A"}
        Type: ${row.discount_type || "N/A"}
        Value: ${row.discount_value || "N/A"}
        Min Amount: ${row.min_amount || "N/A"}
        Max Amount: ${row.max_amount || "N/A"}
        Uses per Customer: ${row.uses_per_customer}
        Total Uses: ${row.total_uses}
        Status: ${row.status ? "Active" : "Inactive"}
      `),
      metadata: {
        type: "club_discount",
        id: row.discount_id,
        name: row.discount_name,
        code: row.discount_code,
        club_id: row.fk_club_id,
        club_name: row.club_name,
        discount_type: row.discount_type,
        status: row.status,
      },
    }));
  }

  async vectorizeFAQCategories() {
    const query = `SELECT * FROM "FAQCategory" WHERE category_name IS NOT NULL`;
    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `faq_category_${row.categroy_id}`,
      content: this.cleanText(`
        FAQ Category: ${row.category_name}
        Description: ${row.description || ""}
        Product: ${row.product || "N/A"}
      `),
      metadata: {
        type: "faq_category",
        id: row.categroy_id,
        name: row.category_name,
        product: row.product,
      },
    }));
  }

  async vectorizeFAQQuestions() {
    const query = `
      SELECT 
        fq.*,
        fc.category_name,
        c.club_name,
        r.restaurant_name,
        s.shop_name,
        b.branch_name
      FROM "FAQQuestions" fq
      LEFT JOIN "FAQCategory" fc ON fq.fk_category_id = fc.categroy_id
      LEFT JOIN "Clubs" c ON fq.fk_club_id = c.club_id
      LEFT JOIN "Restaurants" r ON fq.fk_restaurant_id = r.restaurant_id
      LEFT JOIN "Shops" s ON fq.fk_shop_id = s.shop_id
      LEFT JOIN "Branches" b ON fq.fk_branch_id = b.branch_id
      WHERE fq.question IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `faq_${row.question_id}`,
      content: this.cleanText(`
        Question: ${row.question}
        Answer: ${row.answer || ""}
        Category: ${row.category_name || "N/A"}
        Club: ${row.club_name || "N/A"}
        Restaurant: ${row.restaurant_name || "N/A"}
        Shop: ${row.shop_name || "N/A"}
        Branch: ${row.branch_name || "N/A"}
      `),
      metadata: {
        type: "faq",
        id: row.question_id,
        question: row.question,
        category_name: row.category_name,
        club_name: row.club_name,
        restaurant_name: row.restaurant_name,
        shop_name: row.shop_name,
        branch_name: row.branch_name,
      },
    }));
  }

  async vectorizeFacilities() {
    const query = `
      SELECT 
        f.*,
        c.club_name
      FROM "Facilities" f
      LEFT JOIN "Clubs" c ON f.fk_club_id = c.club_id
      WHERE f.facility_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `facility_${row.facility_id}`,
      content: this.cleanText(`
        Facility: ${row.facility_name}
        Club: ${row.club_name || "N/A"}
        Category: ${row.facility_category}
        Type: ${row.facility_type}
        Time Slot Duration: ${row.time_slot_duration}
        Court Types: ${this.formatArray(row.court_types)}
        Pricing: ${this.formatJSON(row.pricing)}
        Timing: ${this.formatJSON(row.facility_timing)}
        Pricing Option: ${row.pricing_option || "N/A"}
        Minimum Duration: ${row.facility_min_duration} minutes
        Status: ${row.status ? "Active" : "Inactive"}
      `),
      metadata: {
        type: "facility",
        id: row.facility_id,
        name: row.facility_name,
        club_id: row.fk_club_id,
        club_name: row.club_name,
        category: row.facility_category,
        facility_type: row.facility_type,
        status: row.status,
      },
    }));
  }

  async vectorizeFoodDiscounts() {
    const query = `
      SELECT 
        fd.*,
        r.restaurant_name
      FROM "FoodDiscounts" fd
      LEFT JOIN "Restaurants" r ON fd.fk_restaurant_id = r.restaurant_id
      WHERE fd.discount_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `food_discount_${row.discount_id}`,
      content: this.cleanText(`
        Food Discount: ${row.discount_name}
        Code: ${row.discount_code || "N/A"}
        Restaurant: ${row.restaurant_name || "N/A"}
        Type: ${row.discount_type}
        Value: ${row.discount_value}
        Min Amount: ${row.min_amount || "N/A"}
        Max Amount: ${row.max_amount || "N/A"}
        Uses per Customer: ${row.uses_per_customer}
        Total Uses: ${row.total_uses || "N/A"}
        Usage Method: ${row.usage_method || "N/A"}
        Status: ${row.status ? "Active" : "Inactive"}
      `),
      metadata: {
        type: "food_discount",
        id: row.discount_id,
        name: row.discount_name,
        code: row.discount_code,
        restaurant_id: row.fk_restaurant_id,
        restaurant_name: row.restaurant_name,
        discount_type: row.discount_type,
        status: row.status,
      },
    }));
  }

  async vectorizeFoodItemTags() {
    const query = `SELECT * FROM "FoodItemTags" WHERE tag_name IS NOT NULL`;
    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `food_tag_${row.tag_id}`,
      content: this.cleanText(`
        Food Tag: ${row.tag_name}
        Reference: ${row.tag_reference || "N/A"}
      `),
      metadata: {
        type: "food_tag",
        id: row.tag_id,
        name: row.tag_name,
        reference: row.tag_reference,
      },
    }));
  }

  async vectorizeFoodLandingPages() {
    const query = `
      SELECT 
        flp.*,
        r.restaurant_name,
        b.branch_name,
        r.is_only_for_listing
      FROM "FoodLandingPage" flp
      LEFT JOIN "Restaurants" r ON flp.fk_restaurant_id = r.restaurant_id
      LEFT JOIN "Branches" b ON flp.branch_id = b.branch_id
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `food_landing_${row.fk_restaurant_id}_${row.branch_id}`,
      content: this.cleanText(`
        Restaurant: ${row.restaurant_name || "N/A"}
        Branch: ${row.branch_name || "N/A"}
        Emirate: ${row.branch_emirate || "N/A"}
        Average Spend: ${row.average_spend || "N/A"}
        Tags: ${this.formatArray(row.branch_tags)}
        Popular Dishes: ${this.formatArray(row.popular_dishes)}
        Known For: ${row.known_for || ""}
        More Info: ${this.formatArray(row.more_info)}
        Landline Numbers: ${this.formatArray(row.restaurant_landline_numbers)}
        Social Links: ${this.formatJSON(row.social_links)}
        Is only for Listing(Can not be ordered directly from Cravin): ${row.is_only_for_listing ? "Yes" : "No"}
      `),
      metadata: {
        type: "food_landing",
        restaurant_id: row.fk_restaurant_id,
        branch_id: row.branch_id,
        restaurant_name: row.restaurant_name,
        branch_name: row.branch_name,
        emirate: row.branch_emirate,
        show_in_landing: row.show_in_landing_page,
      },
    }));
  }

  async vectorizeItems() {
    const query = `
      SELECT 
        i.*,
        c.category_name,
        b.branch_name,
        r.restaurant_name,
        t.tag_name
      FROM "Items" i
      LEFT JOIN "Categories" c ON i.fk_category_id = c.category_id
      LEFT JOIN "Branches" b ON i.fk_branch_id = b.branch_id
      LEFT JOIN "Restaurants" r ON b.fk_restaurant_id = r.restaurant_id
      LEFT JOIN "FoodItemTags" t ON i.fk_tag_id = t.tag_id
      WHERE i.item_name IS NOT NULL
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `item_${row.item_id}`,
      content: this.cleanText(`
        Food Item: ${row.item_name}
        Description: ${row.item_description || ""}
        Category: ${row.category_name || "N/A"}
        Branch: ${row.branch_name || "N/A"}
        Restaurant: ${row.restaurant_name || "N/A"}
        Type: ${row.item_type}
        Price: ${row.item_price}
        Status: ${row.item_status}
        Tag: ${row.tag_name || "N/A"}
        Recommended: ${row.is_recommended ? "Yes" : "No"}
        Spicy: ${row.is_spicy ? "Yes" : "No"}
        Variants: ${this.formatJSON(row.item_variants)}
        Add-ons: ${this.formatJSON(row.item_add_ons_group)}
        Rating: ${
          row.rating_sum && row.rating_count
            ? (row.rating_sum / row.rating_count).toFixed(1)
            : "N/A"
        }
      `),
      metadata: {
        type: "item",
        id: row.item_id,
        name: row.item_name,
        category: row.category_name,
        branch_id: row.fk_branch_id,
        branch_name: row.branch_name,
        restaurant_name: row.restaurant_name,
        item_type: row.item_type,
        price: row.item_price,
        status: row.item_status,
        is_recommended: row.is_recommended,
        is_spicy: row.is_spicy,
      },
    }));
  }

  async vectorizeRestaurants() {
    const query = `SELECT * FROM "Restaurants" WHERE restaurant_name IS NOT NULL`;
    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `restaurant_${row.restaurant_id}`,
      content: this.cleanText(`
        Restaurant: ${row.restaurant_name}
        Takes Orders: ${row.take_orders ? "Yes" : "No"}
        Menu Type: ${row.restaurant_menu_type || "N/A"}
        Order Policy: ${this.formatArray(row.order_policy)}
        Payment Methods: ${this.formatJSON(row.payment_methods)}
        Timings: ${this.formatJSON(row.restaurant_timings)}
        Social Links: ${this.formatJSON(row.restaurant_social_links)}
        Subscription Status: ${row.subscription_status ? "Active" : "Inactive"}
        Auto Accept: ${row.restaurant_auto_accept ? "Yes" : "No"}
        Support Enabled: ${row.enable_support ? "Yes" : "No"}
        Pickup Module: ${row.pickup_module ? "Available" : "Not Available"}
        Order Link through Whatsapp: https://wa.me/${row.phone_number}
        This Restaurant Under Cravin(can order your favorite food through directly): ${row.is_only_for_listing ? "No" : "Yes"}
      `),
      metadata: {
        type: "restaurant",
        id: row.restaurant_id,
        name: row.restaurant_name,
        phone: row.phone_number,
        takes_orders: row.take_orders,
        menu_type: row.restaurant_menu_type,
        subscription_status: row.subscription_status,
        pickup_available: row.pickup_module,
        order_link_through_whatsapp: "https://wa.me/" + row.phone_number,
        restaurant_under_cravin: !row.is_only_for_listing,
      },
    }));
  }

  async vectorizeShops() {
    const query = `SELECT * FROM "Shops" WHERE shop_name IS NOT NULL`;
    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `shop_${row.shop_id}`,
      content: this.cleanText(`
        Shop: ${row.shop_name}
        Phone: ${row.phone_number}
        Takes Orders: ${row.take_orders ? "Yes" : "No"}
        Order Policy: ${this.formatArray(row.order_policy)}
        Payment Methods: ${this.formatJSON(row.payment_methods)}
        Timings: ${this.formatJSON(row.shop_timings)}
        Social Links: ${this.formatJSON(row.shop_social_links)}
        Subscription Status: ${row.subscription_status ? "Active" : "Inactive"}
        Auto Accept: ${row.shop_auto_accept ? "Yes" : "No"}
        Support Enabled: ${row.enable_support ? "Yes" : "No"}
        Pickup Module: ${row.pickup_module ? "Available" : "Not Available"}
        Generate Quotation: ${row.generate_quotation ? "Yes" : "No"}
        Order Link through Whatsapp: https://wa.me/${row.phone_number}
        This Shop Under Cravin(can order your favorite food through directly): ${row.is_only_for_listing ? "No" : "Yes"}
      `),
      metadata: {
        type: "shop",
        id: row.shop_id,
        name: row.shop_name,
        phone: row.phone_number,
        takes_orders: row.take_orders,
        subscription_status: row.subscription_status,
        pickup_available: row.pickup_module,
        order_link_through_whatsapp: "https://wa.me/" + row.phone_number,
        shop_under_cravin: !row.is_only_for_listing,
      },
    }));
  }

  async vectorizeSportsLandingPages() {
    const query = `
      SELECT 
        slp.*,
        c.club_name,
        c.is_only_for_listing
      FROM "SportsLandingPage" slp
      LEFT JOIN "Clubs" c ON slp.club_id = c.club_id
    `;

    const result = await this.client.query(query);

    return result.rows.map((row) => ({
      id: `sports_landing_${row.club_id}`,
      content: this.cleanText(`
        Club: ${row.club_name || "N/A"}
        Emirate: ${row.club_emirate || "N/A"}
        Average Spend: ${row.average_spend || "N/A"}
        Tags: ${this.formatArray(row.club_tags)}
        Amenities: ${this.formatArray(row.amenities)}
        About Venue: ${row.about_venue || ""}
        Landline Numbers: ${this.formatArray(row.club_landline_numbers)}
        Location: ${
          row.latitude && row.longitude
            ? `${row.latitude}, ${row.longitude}`
            : "N/A"
        }
        Social Links: ${this.formatJSON(row.social_links)}
        Facility Details: ${this.formatJSON(row.facility_details)}
        Is only for Listing(Can not be ordered directly from Cravin): ${row.is_only_for_listing ? "Yes" : "No"}
      `),
      metadata: {
        type: "sports_landing",
        club_id: row.club_id,
        club_name: row.club_name,
        emirate: row.club_emirate,
        show_in_landing: row.show_in_landing_page,
        latitude: row.latitude,
        longitude: row.longitude,
        club_under_cravin: !row.is_only_for_listing,
      },
    }));
  }

  async vectorizeAllTables() {
    console.log("Starting vectorization process...");

    const vectorData = [];

    try {
      const tables = [
        { name: "AddOns", method: this.vectorizeAddOns },
        { name: "Branches", method: this.vectorizeBranches },
        { name: "Categories", method: this.vectorizeCategories },
        { name: "Clubs", method: this.vectorizeClubs },
        { name: "Courts", method: this.vectorizeCourts },
        { name: "DeliveryZones", method: this.vectorizeDeliveryZones },
        { name: "Discounts", method: this.vectorizeDiscounts },
        { name: "FAQCategory", method: this.vectorizeFAQCategories },
        { name: "FAQQuestions", method: this.vectorizeFAQQuestions },
        { name: "Facilities", method: this.vectorizeFacilities },
        { name: "FoodDiscounts", method: this.vectorizeFoodDiscounts },
        { name: "FoodItemTags", method: this.vectorizeFoodItemTags },
        { name: "FoodLandingPage", method: this.vectorizeFoodLandingPages },
        { name: "Items", method: this.vectorizeItems },
        { name: "Restaurants", method: this.vectorizeRestaurants },
        { name: "Shops", method: this.vectorizeShops },
        { name: "SportsLandingPage", method: this.vectorizeSportsLandingPages },
      ];

      for (const table of tables) {
        console.log(`Vectorizing ${table.name}...`);
        try {
          const data = await table.method.call(this);
          vectorData.push(...data);
          console.log(`${table.name}: ${data.length} records processed`);
        } catch (error) {
          console.error(`Error vectorizing ${table.name}:`, error.message);
        }
      }

      console.log(`\nTotal records vectorized: ${vectorData.length}`);
      // return vectorData;
      await this.saveToFile(vectorData);
    } catch (error) {
      console.error("Error in vectorization process:", error);
      throw error;
    }
  }

  async saveToFile(vectorData, filename = "vectorized_data.json") {
    const fs = require("fs");

    const output = {
      timestamp: new Date().toISOString(),
      total_records: vectorData.length,
      data: vectorData,
    };

    fs.writeFileSync(filename, JSON.stringify(output, null, 2));
    console.log(`Data saved to ${filename}`);
  }

  async getIncrementalUpdates(lastUpdateTime) {
    console.log(`Getting incremental updates since: ${lastUpdateTime}`);

    const vectorData = [];
    const modifiableQueries = [
      {
        name: "AddOns",
        query: `SELECT * FROM "AddOns" WHERE modified_at > $1 OR created_at > $1`,
        method: this.vectorizeAddOnsIncremental,
      },
      {
        name: "ShopAddOns",
        query: `SELECT * FROM "ShopAddOns" WHERE modified_at > $1 OR created_at > $1`,
        method: this.vectorizeShopAddOnsIncremental,
      },
    ];

    const creatableQueries = [
      {
        name: "Branches",
        query: `SELECT * FROM "Branches" WHERE created_at > $1`,
        method: this.vectorizeBranchesIncremental,
      },
      {
        name: "Categories",
        query: `SELECT * FROM "Categories" WHERE created_at > $1`,
        method: this.vectorizeCategoriesIncremental,
      },
      {
        name: "Items",
        query: `SELECT * FROM "Items" WHERE created_at > $1`,
        method: this.vectorizeItemsIncremental,
      },
      {
        name: "Restaurants",
        query: `SELECT * FROM "Restaurants" WHERE created_at > $1`,
        method: this.vectorizeRestaurantsIncremental,
      },
      {
        name: "Shops",
        query: `SELECT * FROM "Shops" WHERE created_at > $1`,
        method: this.vectorizeShopsIncremental,
      },
      {
        name: "Clubs",
        query: `SELECT * FROM "Clubs" WHERE created_at > $1`,
        method: this.vectorizeClubsIncremental,
      },
      {
        name: "Facilities",
        query: `SELECT * FROM "Facilities" WHERE created_at > $1`,
        method: this.vectorizeFacilitiesIncremental,
      },
      {
        name: "FoodDiscounts",
        query: `SELECT * FROM "FoodDiscounts" WHERE created_at > $1`,
        method: this.vectorizeFoodDiscountsIncremental,
      },
      {
        name: "ShopDiscounts",
        query: `SELECT * FROM "ShopDiscounts" WHERE created_at > $1`,
        method: this.vectorizeShopDiscountsIncremental,
      },
      {
        name: "Discounts",
        query: `SELECT * FROM "Discounts" WHERE created_at > $1`,
        method: this.vectorizeDiscountsIncremental,
      },
    ];

    const allQueries = [...modifiableQueries, ...creatableQueries];

    for (const queryConfig of allQueries) {
      try {
        const result = await this.client.query(queryConfig.query, [
          lastUpdateTime,
        ]);
        if (result.rows.length > 0) {
          const data = await queryConfig.method.call(this, result.rows);
          vectorData.push(...data);
          console.log(`${queryConfig.name}: ${data.length} updated records`);
        }
      } catch (error) {
        console.error(
          `Error getting incremental updates for ${queryConfig.name}:`,
          error.message
        );
      }
    }

    return vectorData;
  }

  async vectorizeAddOnsIncremental(rows) {
    return rows.map((row) => ({
      id: `addon_${row.add_on_id}`,
      content: this.cleanText(`
        Add-on: ${row.add_on_name}
        Type: ${row.add_on_type}
        Price: ${row.add_on_price}
        Branch ID: ${row.fk_branch_id || "N/A"}
      `),
      metadata: {
        type: "addon",
        id: row.add_on_id,
        name: row.add_on_name,
        addon_type: row.add_on_type,
        price: row.add_on_price,
        branch_id: row.fk_branch_id,
      },
    }));
  }

  async processInBatches(method, batchSize = 1000) {
    const allData = [];
    let offset = 0;
    let hasMore = true;

    while (hasMore) {
      try {
        const batchData = await method.call(this, offset, batchSize);
        allData.push(...batchData);

        if (batchData.length < batchSize) {
          hasMore = false;
        } else {
          offset += batchSize;
        }

        console.log(`Processed batch: ${offset} records`);
      } catch (error) {
        console.error(`Error processing batch at offset ${offset}:`, error);
        hasMore = false;
      }
    }

    return allData;
  }
}

async function main() {
  const fs = require("fs");

const caCert = fs.readFileSync("cert-bundle.pem");
  const vectorizer = new VectorizationService({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_DATABASE,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
   ssl: {
    ca: caCert,
  },
  });

  try {
    await vectorizer.connect();
    console.log("Connected to database");
    await vectorizer.vectorizeAllTables();
    // const lastUpdate = '2025-01-01T00:00:00Z';
    // const incrementalData = await vectorizer.getIncrementalUpdates(lastUpdate);

    console.log("Vectorization completed successfully!");
  } catch (error) {
    console.error("Error:", error);
  } finally {
    await vectorizer.disconnect();
  }
}

module.exports = { VectorizationService };

if (require.main === module) {
  main();
}
