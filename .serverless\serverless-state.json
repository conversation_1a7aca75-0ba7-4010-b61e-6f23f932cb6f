{"service": {"service": "cravin-concierge-whatsapp", "serviceObject": {"name": "cravin-concierge-whatsapp"}, "provider": {"name": "aws", "runtime": "nodejs20.x", "region": "us-east-1", "stage": "prod", "memorySize": 512, "timeout": 30, "httpApi": {"cors": true}, "environment": {"NODE_ENV": "prod", "AWS_REGION": "us-east-1", "WHATSAPP_VERIFY_TOKEN": "WeBhoOk", "WHATSAPP_ACCESS_TOKEN": "EAAFbMv2U8kYBO3Tky9kHY7aNlrTwsKZA7JoA3yZAzUYAmOnXsMZB8nT3m68a9WRhlmwyClWhW02oF8jq0ZCBDpBDOEGibgQZCowNWMz6YvQFPv87daMLFlkEm7uXRvheGR0lkdCf4f2j0vza0ZAZBKYLKvC2B30QUM2cZCkyVPgCbneWj4CO2wR7M0CWI2rCCEUW9JZASCF9YCcZADtuvobUwqrGpmJnmoG7Ausy9rm3hO2Uc9w4amg6gUOwKWR0ZAk", "WHATSAPP_PHONE_NUMBER_ID": "394218977097517", "WHATSAPP_API_VERSION": "v18.0", "WHATSAPP_WEBHOOK_SECRET": "WeBhoOk", "BEDROCK_MODEL_ID": "meta.llama3-8b-instruct-v1:0", "BEDROCK_AGENT_ID": "R1FMKXHBQF", "BEDROCK_AGENT_ALIAS_ID": "TSTALIASID", "DB_HOST": "ep-throbbing-union-48745705.ap-southeast-1.aws.neon.tech", "DB_PORT": "5432", "DB_NAME": "cravin-booking-demo", "DB_USERNAME": "CravinUAE", "DB_PASSWORD": "qIuJ6oV3gwzX", "DB_SSL": "true", "LOG_LEVEL": "info"}, "iam": {"role": {"statements": [{"Effect": "Allow", "Action": ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream"], "Resource": "arn:aws:bedrock:us-east-1::foundation-model/*"}, {"Effect": "Allow", "Action": ["bedrock:InvokeAgent"], "Resource": ["arn:aws:bedrock:us-east-1:*:agent/*", "arn:aws:bedrock:us-east-1:*:agent-alias/*"]}, {"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": "arn:aws:logs:us-east-1:*:*"}]}}, "versionFunctions": true, "compiledCloudFormationTemplate": {"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"WhatsappWebhookLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/lambda/cravin-concierge-whatsapp-prod-whatsappWebhook", "RetentionInDays": 14}}, "IamRoleLambdaExecution": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}, "Policies": [{"PolicyName": {"Fn::Join": ["-", ["cravin-concierge-whatsapp", "prod", "lambda"]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogStream", "logs:CreateLogGroup", "logs:TagResource"], "Resource": [{"Fn::Sub": "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/cravin-concierge-whatsapp-prod*:*"}]}, {"Effect": "Allow", "Action": ["logs:PutLogEvents"], "Resource": [{"Fn::Sub": "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/cravin-concierge-whatsapp-prod*:*:*"}]}, {"$ref": "$[\"service\"][\"provider\"][\"iam\"][\"role\"][\"statements\"][0]"}, {"$ref": "$[\"service\"][\"provider\"][\"iam\"][\"role\"][\"statements\"][1]"}, {"$ref": "$[\"service\"][\"provider\"][\"iam\"][\"role\"][\"statements\"][2]"}]}}], "Path": "/", "RoleName": {"Fn::Join": ["-", ["cravin-concierge-whatsapp", "prod", {"Ref": "AWS::Region"}, "lambdaRole"]]}}}, "WhatsappWebhookLambdaFunction": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "serverless-framework-deployments-us-east-1-6f415178-284f", "S3Key": "serverless/cravin-concierge-whatsapp/prod/*************-2025-07-22T05:17:40.289Z/cravin-concierge-whatsapp.zip"}, "Handler": "index.handler", "Runtime": "nodejs20.x", "FunctionName": "cravin-concierge-whatsapp-prod-whatsappWebhook", "MemorySize": 512, "Timeout": 30, "Description": "Handles WhatsApp webhook verification and incoming messages with AI processing", "Environment": {"Variables": {"NODE_ENV": "prod", "AWS_REGION": "us-east-1", "WHATSAPP_VERIFY_TOKEN": "WeBhoOk", "WHATSAPP_ACCESS_TOKEN": "EAAFbMv2U8kYBO3Tky9kHY7aNlrTwsKZA7JoA3yZAzUYAmOnXsMZB8nT3m68a9WRhlmwyClWhW02oF8jq0ZCBDpBDOEGibgQZCowNWMz6YvQFPv87daMLFlkEm7uXRvheGR0lkdCf4f2j0vza0ZAZBKYLKvC2B30QUM2cZCkyVPgCbneWj4CO2wR7M0CWI2rCCEUW9JZASCF9YCcZADtuvobUwqrGpmJnmoG7Ausy9rm3hO2Uc9w4amg6gUOwKWR0ZAk", "WHATSAPP_PHONE_NUMBER_ID": "394218977097517", "WHATSAPP_API_VERSION": "v18.0", "WHATSAPP_WEBHOOK_SECRET": "WeBhoOk", "BEDROCK_MODEL_ID": "meta.llama3-8b-instruct-v1:0", "BEDROCK_AGENT_ID": "R1FMKXHBQF", "BEDROCK_AGENT_ALIAS_ID": "TSTALIASID", "DB_HOST": "ep-throbbing-union-48745705.ap-southeast-1.aws.neon.tech", "DB_PORT": "5432", "DB_NAME": "cravin-booking-demo", "DB_USERNAME": "CravinUAE", "DB_PASSWORD": "qIuJ6oV3gwzX", "DB_SSL": "true", "LOG_LEVEL": "info", "FUNCTION_NAME": "whatsappWebhook"}}, "Role": {"Fn::GetAtt": ["IamRoleLambdaExecution", "<PERSON><PERSON>"]}}, "DependsOn": ["WhatsappWebhookLogGroup"]}, "WhatsappWebhookLambdaVersion234BIwXQssP8y2urSmNh6vIaUh0yRNKYdZRsOt09BOE": {"Type": "AWS::Lambda::Version", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"FunctionName": {"Ref": "WhatsappWebhookLambdaFunction"}, "CodeSha256": "JoU8RJWFOnV4pPiMPfybTnh+XFDmCDt/L/Su6tPJ0FY=", "Description": "Handles WhatsApp webhook verification and incoming messages with AI processing"}}, "HttpApi": {"Type": "AWS::ApiGatewayV2::Api", "Properties": {"Name": "prod-cravin-concierge-whatsapp", "ProtocolType": "HTTP", "CorsConfiguration": {"AllowHeaders": ["Content-Type", "X-Amz-Date", "Authorization", "X-Api-Key", "X-Amz-Security-Token", "X-Amz-User-Agent", "X-Amzn-Trace-Id"], "AllowMethods": ["OPTIONS", "GET", "POST"], "AllowOrigins": ["*"]}}}, "HttpApiStage": {"Type": "AWS::ApiGatewayV2::Stage", "Properties": {"ApiId": {"Ref": "HttpApi"}, "StageName": "$default", "AutoDeploy": true, "DefaultRouteSettings": {"DetailedMetricsEnabled": false}}}, "WhatsappWebhookLambdaPermissionHttpApi": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Fn::GetAtt": ["WhatsappWebhookLambdaFunction", "<PERSON><PERSON>"]}, "Action": "lambda:InvokeFunction", "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "HttpApi"}, "/*"]]}}}, "HttpApiIntegrationWhatsappWebhook": {"Type": "AWS::ApiGatewayV2::Integration", "Properties": {"ApiId": {"Ref": "HttpApi"}, "IntegrationType": "AWS_PROXY", "IntegrationUri": {"$ref": "$[\"service\"][\"provider\"][\"compiledCloudFormationTemplate\"][\"Resources\"][\"WhatsappWebhookLambdaPermissionHttpApi\"][\"Properties\"][\"FunctionName\"]"}, "PayloadFormatVersion": "2.0", "TimeoutInMillis": 30000}}, "HttpApiRouteGetWebhook": {"Type": "AWS::ApiGatewayV2::Route", "Properties": {"ApiId": {"Ref": "HttpApi"}, "RouteKey": "GET /webhook", "Target": {"Fn::Join": ["/", ["integrations", {"Ref": "HttpApiIntegrationWhatsappWebhook"}]]}}, "DependsOn": "HttpApiIntegrationWhatsappWebhook"}, "HttpApiRoutePostWebhook": {"Type": "AWS::ApiGatewayV2::Route", "Properties": {"ApiId": {"Ref": "HttpApi"}, "RouteKey": "POST /webhook", "Target": {"Fn::Join": ["/", ["integrations", {"Ref": "HttpApiIntegrationWhatsappWebhook"}]]}}, "DependsOn": "HttpApiIntegrationWhatsappWebhook"}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-6f415178-284f", "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-ServerlessDeploymentBucketName"}}, "WhatsappWebhookLambdaFunctionQualifiedArn": {"Description": "Current Lambda function version", "Value": {"Ref": "WhatsappWebhookLambdaVersion234BIwXQssP8y2urSmNh6vIaUh0yRNKYdZRsOt09BOE"}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-WhatsappWebhookLambdaFunctionQualifiedArn"}}, "HttpApiId": {"Description": "Id of the HTTP API", "Value": {"Ref": "HttpApi"}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-HttpApiId"}}, "HttpApiUrl": {"Description": "URL of the HTTP API", "Value": {"Fn::Join": ["", ["https://", {"Ref": "HttpApi"}, ".execute-api.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}]]}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-HttpApiUrl"}}, "WebhookUrl": {"Description": "WhatsApp Webhook URL", "Value": {"Fn::Join": ["", ["https://", {"Ref": "HttpApi"}, ".execute-api.", "us-east-1", ".amazonaws.com/webhook"]]}}, "ServiceName": {"Description": "Service name", "Value": "cravin-concierge-whatsapp"}, "Stage": {"Description": "Deployment stage", "Value": "prod"}}}, "vpc": {}}, "custom": {"serverless-offline": {"httpPort": 3000, "host": "localhost", "stage": "local"}, "stages": {"dev": {"memorySize": 512, "timeout": 30}, "prod": {"memorySize": 1024, "timeout": 60}}}, "plugins": ["serverless-offline"], "pluginsData": {}, "functions": {"whatsappWebhook": {"handler": "index.handler", "description": "Handles WhatsApp webhook verification and incoming messages with AI processing", "events": [{"httpApi": {"path": "/webhook", "method": "get"}, "resolvedMethod": "GET", "resolvedPath": "/webhook"}, {"httpApi": {"path": "/webhook", "method": "post"}, "resolvedMethod": "POST", "resolvedPath": "/webhook"}], "environment": {"FUNCTION_NAME": "whatsappWebhook"}, "name": "cravin-concierge-whatsapp-prod-whatsappWebhook", "package": {}, "memory": 512, "timeout": 30, "runtime": "nodejs20.x", "vpc": {}, "versionLogicalId": "WhatsappWebhookLambdaVersion234BIwXQssP8y2urSmNh6vIaUh0yRNKYdZRsOt09BOE"}}, "resources": {"Resources": {"WhatsappWebhookLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/lambda/cravin-concierge-whatsapp-prod-whatsappWebhook", "RetentionInDays": 14}}}, "Outputs": {"WebhookUrl": {"Description": "WhatsApp Webhook URL", "Value": {"Fn::Join": ["", ["https://", {"Ref": "HttpApi"}, ".execute-api.", "us-east-1", ".amazonaws.com/webhook"]]}}, "ServiceName": {"Description": "Service name", "Value": "cravin-concierge-whatsapp"}, "Stage": {"Description": "Deployment stage", "Value": "prod"}}}, "configValidationMode": "warn", "serviceFilename": "serverless", "initialServerlessConfig": {"org": "nookeshkarri7", "app": "cravin-concierge", "service": "cravin-concierge-whatsapp", "provider": {"$ref": "$[\"service\"][\"provider\"]"}, "functions": {"$ref": "$[\"service\"][\"functions\"]"}, "package": {"patterns": ["!node_modules/**", "!.git/**", "!.env*", "!README.md", "!server.mjs", "!test-webhook.mjs", "!cravin-ai/**", "!ai-bot.zip", "!cert-bundle.pem", "!vectors.js", "!vectorized_data.json", "!package-lock.json", "index.mjs", "package.json", "config/**", "handlers/**", "services/**", "utils/**", "node_modules/@aws-sdk/**", "node_modules/pg/**", "node_modules/axios/**", "node_modules/crypto/**"], "artifactsS3KeyDirname": "serverless/cravin-concierge-whatsapp/prod/code-artifacts", "deploymentBucket": "serverless-framework-deployments-us-east-1-6f415178-284f", "artifact": "H:\\Cravin\\Cravin-Concierge\\.serverless\\cravin-concierge-whatsapp.zip", "artifactDirectoryName": "serverless/cravin-concierge-whatsapp/prod/*************-2025-07-22T05:17:40.289Z"}, "plugins": {"$ref": "$[\"service\"][\"plugins\"]"}, "custom": {"$ref": "$[\"service\"][\"custom\"]"}, "resources": {"$ref": "$[\"service\"][\"resources\"]"}}, "app": "cravin-concierge", "appId": null, "org": "nookeshkarri7", "orgId": null, "layers": {}, "artifact": "H:\\Cravin\\Cravin-Concierge\\.serverless\\cravin-concierge-whatsapp.zip"}, "package": {"artifactDirectoryName": "serverless/cravin-concierge-whatsapp/prod/*************-2025-07-22T05:17:40.289Z", "artifact": "cravin-concierge-whatsapp.zip"}}