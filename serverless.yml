# Serverless Framework configuration for Cravin Concierge WhatsApp Lambda
# This service handles WhatsApp webhook verification and message processing with AI capabilities

# Organization and app configuration for Serverless Framework Dashboard
org: nookeshkarri7
app: cravin-concierge
service: cravin-concierge-whatsapp

provider:
  name: aws
  runtime: nodejs20.x
  region: ${opt:region, 'us-east-1'}
  stage: ${opt:stage, 'dev'}
  
  # Memory and timeout configuration
  memorySize: 512
  timeout: 30
  
  # Environment variables
  environment:
    NODE_ENV: ${self:provider.stage}
    AWS_REGION: ${self:provider.region}
    
    # WhatsApp Configuration
    WHATSAPP_VERIFY_TOKEN: ${env:WHATSAPP_VERIFY_TOKEN}
    WHATSAPP_ACCESS_TOKEN: ${env:WHATSAPP_ACCESS_TOKEN}
    WHATSAPP_PHONE_NUMBER_ID: ${env:WHATSAPP_PHONE_NUMBER_ID}
    WHATSAPP_API_VERSION: ${env:WHATSAPP_API_VERSION, 'v18.0'}
    WHATSAPP_WEBHOOK_SECRET: ${env:WHATSAPP_WEBHOOK_SECRET}
    
    # AWS Bedrock Configuration
    BEDROCK_MODEL_ID: ${env:BEDROCK_MODEL_ID, 'anthropic.claude-3-sonnet-20240229-v1:0'}
    BEDROCK_AGENT_ID: ${env:BEDROCK_AGENT_ID}
    BEDROCK_AGENT_ALIAS_ID: ${env:BEDROCK_AGENT_ALIAS_ID, 'TSTALIASID'}
    
    # Database Configuration
    DB_HOST: ${env:DB_HOST}
    DB_PORT: ${env:DB_PORT, '5432'}
    DB_NAME: ${env:DB_NAME}
    DB_USERNAME: ${env:DB_USERNAME}
    DB_PASSWORD: ${env:DB_PASSWORD}
    DB_SSL: ${env:DB_SSL, 'true'}
    
    # Application Configuration
    LOG_LEVEL: ${env:LOG_LEVEL, 'info'}

  # IAM permissions for the Lambda function
  iam:
    role:
      statements:
        # Bedrock permissions for AI processing
        - Effect: Allow
          Action:
            - bedrock:InvokeModel
            - bedrock:InvokeModelWithResponseStream
          Resource: 
            - "arn:aws:bedrock:${self:provider.region}::foundation-model/*"
        
        # Bedrock Agent permissions
        - Effect: Allow
          Action:
            - bedrock:InvokeAgent
          Resource:
            - "arn:aws:bedrock:${self:provider.region}:*:agent/*"
            - "arn:aws:bedrock:${self:provider.region}:*:agent-alias/*"
        
        # CloudWatch Logs permissions
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: "arn:aws:logs:${self:provider.region}:*:*"

  # VPC configuration (uncomment if database is in VPC)
  # vpc:
  #   securityGroupIds:
  #     - ${env:VPC_SECURITY_GROUP_ID}
  #   subnetIds:
  #     - ${env:VPC_SUBNET_ID_1}
  #     - ${env:VPC_SUBNET_ID_2}

functions:
  # Main WhatsApp webhook handler
  whatsappWebhook:
    handler: index.handler
    description: "Handles WhatsApp webhook verification and incoming messages with AI processing"
    events:
      - httpApi:
          path: /webhook
          method: get
          cors: true
      - httpApi:
          path: /webhook
          method: post
          cors: true
    
    # Function-specific environment variables (if needed)
    environment:
      FUNCTION_NAME: whatsappWebhook

# Package configuration
package:
  # Exclude development files and directories
  patterns:
    - '!node_modules/**'
    - '!.git/**'
    - '!.env*'
    - '!README.md'
    - '!server.mjs'
    - '!test-webhook.mjs'
    - '!cravin-ai/**'
    - '!ai-bot.zip'
    - '!cert-bundle.pem'
    - '!vectors.js'
    - '!vectorized_data.json'
    - '!package-lock.json'
    
    # Include necessary files
    - 'index.mjs'
    - 'package.json'
    - 'config/**'
    - 'handlers/**'
    - 'services/**'
    - 'utils/**'
    - 'node_modules/@aws-sdk/**'
    - 'node_modules/pg/**'
    - 'node_modules/axios/**'
    - 'node_modules/crypto/**'

# Plugins
plugins:
  - serverless-offline # For local development

# Custom configuration
custom:
  # Serverless Offline configuration for local development
  serverless-offline:
    httpPort: 3000
    host: localhost
    stage: local
    
  # Stage-specific configurations
  stages:
    dev:
      memorySize: 512
      timeout: 30
    prod:
      memorySize: 1024
      timeout: 60

# Resources (CloudFormation templates)
resources:
  Resources:
    # API Gateway custom domain (optional)
    # WebhookDomain:
    #   Type: AWS::ApiGatewayV2::DomainName
    #   Properties:
    #     DomainName: ${env:CUSTOM_DOMAIN}
    #     DomainNameConfigurations:
    #       - CertificateArn: ${env:SSL_CERTIFICATE_ARN}
    #         EndpointType: REGIONAL
    
    # CloudWatch Log Group with retention
    WhatsappWebhookLogGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /aws/lambda/${self:service}-${self:provider.stage}-whatsappWebhook
        RetentionInDays: 14

  # Outputs
  Outputs:
    WebhookUrl:
      Description: "WhatsApp Webhook URL"
      Value:
        Fn::Join:
          - ""
          - - "https://"
            - Ref: HttpApi
            - ".execute-api."
            - ${self:provider.region}
            - ".amazonaws.com/webhook"
    
    ServiceName:
      Description: "Service name"
      Value: ${self:service}
    
    Stage:
      Description: "Deployment stage"
      Value: ${self:provider.stage}
