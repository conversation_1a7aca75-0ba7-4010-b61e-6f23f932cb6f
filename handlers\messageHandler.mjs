/**
 * WhatsApp Message Handler
 * Processes incoming WhatsApp messages and coordinates AI responses
 */

import { aiService } from '../services/aiService.mjs';
import { whatsappService } from '../services/whatsappService.mjs';
import { userService } from '../services/userService.mjs';
import { validateWebhookSignature } from '../utils/security.mjs';

/**
 * Handles incoming WhatsApp messages
 * @param {Object} event - Lambda event object
 * @returns {Object} Lambda response object
 */
export async function handleIncomingMessage(event) {
  try {
    // console.log('Incoming message event:', JSON.stringify(event, null, 2));

    // Validate webhook signature
    const isValidSignature = validateWebhookSignature(event);
    if (!isValidSignature) {
      console.error('Invalid webhook signature');
      return {
        statusCode: 403,
        body: JSON.stringify({ error: 'Invalid signature' })
      };
    }

    // Parse the webhook payload
    const body = JSON.parse(event.body);
    
    // Check if this is a message event
    if (!body.entry || !body.entry[0] || !body.entry[0].changes) {
      console.log('No message changes found in webhook');
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'No action required' })
      };
    }

    const changes = body.entry[0].changes;
    
    for (const change of changes) {
      if (change.field === 'messages' && change.value.messages) {
        await processMessages(change.value);
      }
    }

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'Messages processed successfully' })
    };

  } catch (error) {
    console.error('Error processing incoming message:', error);
    
    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Internal Server Error',
        message: 'Failed to process incoming message'
      })
    };
  }
}

/**
 * Processes individual messages from WhatsApp
 * @param {Object} messageData - Message data from WhatsApp webhook
 */
async function processMessages(messageData) {
  const messages = messageData.messages || [];
  
  for (const message of messages) {
    try {
      // Skip if not a text message
      if (message.type !== 'text') {
        console.log(`Skipping non-text message of type: ${message.type}`);
        continue;
      }

      const userMessage = message.text.body;
      const fromNumber = message.from;
      const messageId = message.id;

      console.log(`Processing message from ${fromNumber}: ${userMessage}`);

      // Mark message as read
      await whatsappService.markMessageAsRead(messageId);

      // Send typing indicator
      await whatsappService.sendTypingIndicator(messageId);

      // Check if user is introducing themselves and extract name
      await extractAndStoreUserName(userMessage, fromNumber);

      // Process message with AI service
      let aiResponse = await aiService.processUserMessage(userMessage, fromNumber);
      // console.log(aiResponse, "aiResponse");
      // aiResponse+=`https://wa.me/971501234567 Al Fanar Restaurant`;

      // Check if AI response contains wa.me links and send CTA message
      if (await handleWaMeLinks(aiResponse, fromNumber)) {
        continue;
      }

      // Send response back to user
      await whatsappService.sendTextMessage(fromNumber, aiResponse);

      console.log(`Response sent to ${fromNumber}: ${aiResponse}`);

    } catch (error) {
      console.error(`Error processing message ${message.id}:`, error);
      
      // Send error message to user
      try {
        await whatsappService.sendTextMessage(
          message.from,
          "I'm sorry, I'm having trouble processing your message right now. Please try again later."
        );
      } catch (sendError) {
        console.error('Failed to send error message:', sendError);
      }
    }
  }
}

/**
 * Handles wa.me links in AI responses by sending CTA messages
 * @param {string} aiResponse - AI response text
 * @param {string} fromNumber - User's phone number
 * @returns {boolean} True if wa.me links were found and handled
 */
async function handleWaMeLinks(aiResponse, fromNumber) {
  try {
    // Regex to match wa.me links with phone numbers
    const waMeRegex = /https:\/\/wa\.me\/(\+?\d{10,15})(?:\s+([^https\n]+))?/gi;
    const matches = [...aiResponse.matchAll(waMeRegex)];

    if (matches.length === 0) {
      return false;
    }

    // Process the first wa.me link found
    const match = matches[0];
    const phoneNumber = match[1];
    const businessInfo = match[2] ? match[2].trim() : '';

    // Extract the main message text (everything before the first wa.me link)
    const mainText = aiResponse.substring(0, match.index).trim();

    // Create button text - use business info if available, otherwise default
    let buttonText = 'Order/Booking Link';
    // if (businessInfo) {
    //   // Extract business name from the info (take first few words)
    //   const businessName = businessInfo.split(/[,.\n]/)[0].trim();
    //   if (businessName.length > 0 && businessName.length <= 20) {
    //     buttonText = `Contact ${businessName}`;
    //   }
    // }

    // // Ensure button text isn't too long
    // if (buttonText.length > 20) {
    //   buttonText = 'Contact Business';
    // }

    // Create the wa.me URL
    const waUrl = `https://wa.me/${phoneNumber}`;

    // Use main text as body, or a default message if empty
    const bodyText = mainText || 'Here\'s the contact information for the business:';

    console.log(`Sending CTA message with wa.me link: ${waUrl}`);

    // Send CTA message
    await whatsappService.sendCTAMessage(
      fromNumber,
      bodyText,
      buttonText,
      waUrl
    );

    return true;

  } catch (error) {
    console.error('Error handling wa.me links:', error);
    return false;
  }
}

/**
 * Extracts user name from message if they're introducing themselves
 * @param {string} message - User's message
 * @param {string} phoneNumber - User's phone number
 */
async function extractAndStoreUserName(message, phoneNumber) {
  try {
    // Simple patterns to detect name introductions
    const namePatterns = [
      /(?:my name is|i'm|i am|call me|this is)\s+([a-zA-Z\s]{2,30})/i,
      /(?:hi|hello|hey),?\s*(?:my name is|i'm|i am)?\s*([a-zA-Z\s]{2,30})/i,
      /^([a-zA-Z\s]{2,30})\s*(?:here|speaking)$/i
    ];

    for (const pattern of namePatterns) {
      const match = message.match(pattern);
      if (match && match[1]) {
        const extractedName = match[1].trim();

        // Basic validation - ensure it looks like a name
        if (extractedName.length >= 2 && extractedName.length <= 30 &&
            /^[a-zA-Z\s]+$/.test(extractedName) &&
            !extractedName.toLowerCase().includes('whatsapp') &&
            !extractedName.toLowerCase().includes('bot')) {

          console.log(`Extracted name "${extractedName}" for user ${phoneNumber}`);
          // await userService.upsertUser(phoneNumber, extractedName);
          break;
        }
      }
    }
  } catch (error) {
    console.error('Error extracting user name:', error);
    // Don't throw error - name extraction is optional
  }
}
