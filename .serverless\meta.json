{"H:\\Cravin\\Cravin-Concierge\\serverless.yml": {"versionFramework": "4.17.1", "servicePath": "H:\\Cravin\\Cravin-Concierge\\serverless.yml", "serviceConfigFileName": "serverless.yml", "service": {"org": "nookeshkarri7", "app": "cravin-concierge", "service": "cravin-concierge-whatsapp", "provider": {"name": "aws", "runtime": "nodejs20.x", "region": "us-east-1", "stage": "prod", "memorySize": 512, "timeout": 30, "httpApi": {"cors": true}, "environment": {"NODE_ENV": "prod", "AWS_REGION": "us-east-1", "WHATSAPP_VERIFY_TOKEN": "<REDACTED>", "WHATSAPP_ACCESS_TOKEN": "<REDACTED>", "WHATSAPP_PHONE_NUMBER_ID": "394218977097517", "WHATSAPP_API_VERSION": "v18.0", "WHATSAPP_WEBHOOK_SECRET": "<REDACTED>", "BEDROCK_MODEL_ID": "meta.llama3-8b-instruct-v1:0", "BEDROCK_AGENT_ID": "R1FMKXHBQF", "BEDROCK_AGENT_ALIAS_ID": "TSTALIASID", "DB_HOST": "ep-throbbing-union-48745705.ap-southeast-1.aws.neon.tech", "DB_PORT": "5432", "DB_NAME": "cravin-booking-demo", "DB_USERNAME": "CravinUAE", "DB_PASSWORD": "<REDACTED>", "DB_SSL": "true", "LOG_LEVEL": "info"}, "iam": {"role": {"statements": [{"Effect": "Allow", "Action": ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream"], "Resource": "arn:aws:bedrock:us-east-1::foundation-model/*"}, {"Effect": "Allow", "Action": ["bedrock:InvokeAgent"], "Resource": ["arn:aws:bedrock:us-east-1:*:agent/*", "arn:aws:bedrock:us-east-1:*:agent-alias/*"]}, {"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": "arn:aws:logs:us-east-1:*:*"}]}}, "versionFunctions": true, "compiledCloudFormationTemplate": {"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"WhatsappWebhookLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/lambda/cravin-concierge-whatsapp-prod-whatsappWebhook", "RetentionInDays": 14}}, "IamRoleLambdaExecution": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}, "Policies": [{"PolicyName": {"Fn::Join": ["-", ["cravin-concierge-whatsapp", "prod", "lambda"]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogStream", "logs:CreateLogGroup", "logs:TagResource"], "Resource": [{"Fn::Sub": "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/cravin-concierge-whatsapp-prod*:*"}]}, {"Effect": "Allow", "Action": ["logs:PutLogEvents"], "Resource": [{"Fn::Sub": "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/cravin-concierge-whatsapp-prod*:*:*"}]}, {"Effect": "Allow", "Action": ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream"], "Resource": "arn:aws:bedrock:us-east-1::foundation-model/*"}, {"Effect": "Allow", "Action": ["bedrock:InvokeAgent"], "Resource": ["arn:aws:bedrock:us-east-1:*:agent/*", "arn:aws:bedrock:us-east-1:*:agent-alias/*"]}, {"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": "arn:aws:logs:us-east-1:*:*"}]}}], "Path": "/", "RoleName": {"Fn::Join": ["-", ["cravin-concierge-whatsapp", "prod", {"Ref": "AWS::Region"}, "lambdaRole"]]}}}, "WhatsappWebhookLambdaFunction": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "serverless-framework-deployments-us-east-1-6f415178-284f", "S3Key": "serverless/cravin-concierge-whatsapp/prod/*************-2025-07-22T05:22:32.714Z/cravin-concierge-whatsapp.zip"}, "Handler": "index.handler", "Runtime": "nodejs20.x", "FunctionName": "cravin-concierge-whatsapp-prod-whatsappWebhook", "MemorySize": 512, "Timeout": 30, "Description": "Handles WhatsApp webhook verification and incoming messages with AI processing", "Environment": {"Variables": {"NODE_ENV": "prod", "AWS_REGION": "us-east-1", "WHATSAPP_VERIFY_TOKEN": "<REDACTED>", "WHATSAPP_ACCESS_TOKEN": "<REDACTED>", "WHATSAPP_PHONE_NUMBER_ID": "394218977097517", "WHATSAPP_API_VERSION": "v18.0", "WHATSAPP_WEBHOOK_SECRET": "<REDACTED>", "BEDROCK_MODEL_ID": "meta.llama3-8b-instruct-v1:0", "BEDROCK_AGENT_ID": "R1FMKXHBQF", "BEDROCK_AGENT_ALIAS_ID": "TSTALIASID", "DB_HOST": "ep-throbbing-union-48745705.ap-southeast-1.aws.neon.tech", "DB_PORT": "5432", "DB_NAME": "cravin-booking-demo", "DB_USERNAME": "CravinUAE", "DB_PASSWORD": "<REDACTED>", "DB_SSL": "true", "LOG_LEVEL": "info", "FUNCTION_NAME": "whatsappWebhook"}}, "Role": {"Fn::GetAtt": ["IamRoleLambdaExecution", "<PERSON><PERSON>"]}}, "DependsOn": ["WhatsappWebhookLogGroup"]}, "WhatsappWebhookLambdaVersion234BIwXQssP8y2urSmNh6vIaUh0yRNKYdZRsOt09BOE": {"Type": "AWS::Lambda::Version", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"FunctionName": {"Ref": "WhatsappWebhookLambdaFunction"}, "CodeSha256": "JoU8RJWFOnV4pPiMPfybTnh+XFDmCDt/L/Su6tPJ0FY=", "Description": "Handles WhatsApp webhook verification and incoming messages with AI processing"}}, "HttpApi": {"Type": "AWS::ApiGatewayV2::Api", "Properties": {"Name": "prod-cravin-concierge-whatsapp", "ProtocolType": "HTTP", "CorsConfiguration": {"AllowCredentials": "<REDACTED>", "AllowHeaders": ["Content-Type", "X-Amz-Date", "Authorization", "X-Api-Key", "X-Amz-Security-Token", "X-Amz-User-Agent", "X-Amzn-Trace-Id"], "AllowMethods": ["OPTIONS", "GET", "POST"], "AllowOrigins": ["*"]}}}, "HttpApiStage": {"Type": "AWS::ApiGatewayV2::Stage", "Properties": {"ApiId": {"Ref": "HttpApi"}, "StageName": "$default", "AutoDeploy": true, "DefaultRouteSettings": {"DetailedMetricsEnabled": false}}}, "WhatsappWebhookLambdaPermissionHttpApi": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Fn::GetAtt": ["WhatsappWebhookLambdaFunction", "<PERSON><PERSON>"]}, "Action": "lambda:InvokeFunction", "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "HttpApi"}, "/*"]]}}}, "HttpApiIntegrationWhatsappWebhook": {"Type": "AWS::ApiGatewayV2::Integration", "Properties": {"ApiId": {"Ref": "HttpApi"}, "IntegrationType": "AWS_PROXY", "IntegrationUri": {"Fn::GetAtt": ["WhatsappWebhookLambdaFunction", "<PERSON><PERSON>"]}, "PayloadFormatVersion": "2.0", "TimeoutInMillis": 30000}}, "HttpApiRouteGetWebhook": {"Type": "AWS::ApiGatewayV2::Route", "Properties": {"ApiId": {"Ref": "HttpApi"}, "RouteKey": "GET /webhook", "Target": {"Fn::Join": ["/", ["integrations", {"Ref": "HttpApiIntegrationWhatsappWebhook"}]]}}, "DependsOn": "HttpApiIntegrationWhatsappWebhook"}, "HttpApiRoutePostWebhook": {"Type": "AWS::ApiGatewayV2::Route", "Properties": {"ApiId": {"Ref": "HttpApi"}, "RouteKey": "POST /webhook", "Target": {"Fn::Join": ["/", ["integrations", {"Ref": "HttpApiIntegrationWhatsappWebhook"}]]}}, "DependsOn": "HttpApiIntegrationWhatsappWebhook"}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-6f415178-284f", "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-ServerlessDeploymentBucketName"}}, "WhatsappWebhookLambdaFunctionQualifiedArn": {"Description": "Current Lambda function version", "Value": {"Ref": "WhatsappWebhookLambdaVersion234BIwXQssP8y2urSmNh6vIaUh0yRNKYdZRsOt09BOE"}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-WhatsappWebhookLambdaFunctionQualifiedArn"}}, "HttpApiId": {"Description": "Id of the HTTP API", "Value": {"Ref": "HttpApi"}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-HttpApiId"}}, "HttpApiUrl": {"Description": "URL of the HTTP API", "Value": {"Fn::Join": ["", ["https://", {"Ref": "HttpApi"}, ".execute-api.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}]]}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-HttpApiUrl"}}, "WebhookUrl": {"Description": "WhatsApp Webhook URL", "Value": {"Fn::Join": ["", ["https://", {"Ref": "HttpApi"}, ".execute-api.", "us-east-1", ".amazonaws.com/webhook"]]}}, "ServiceName": {"Description": "Service name", "Value": "cravin-concierge-whatsapp"}, "Stage": {"Description": "Deployment stage", "Value": "prod"}}}, "vpc": {}}, "functions": {"whatsappWebhook": {"handler": "index.handler", "description": "Handles WhatsApp webhook verification and incoming messages with AI processing", "events": [{"httpApi": {"path": "/webhook", "method": "get"}, "resolvedMethod": "GET", "resolvedPath": "/webhook"}, {"httpApi": {"path": "/webhook", "method": "post"}, "resolvedMethod": "POST", "resolvedPath": "/webhook"}], "environment": {"FUNCTION_NAME": "whatsappWebhook"}, "name": "cravin-concierge-whatsapp-prod-whatsappWebhook", "package": {}, "memory": 512, "timeout": 30, "runtime": "nodejs20.x", "vpc": {}, "versionLogicalId": "WhatsappWebhookLambdaVersion234BIwXQssP8y2urSmNh6vIaUh0yRNKYdZRsOt09BOE"}}, "package": {"patterns": ["!node_modules/**", "!.git/**", "!.env*", "!README.md", "!server.mjs", "!test-webhook.mjs", "!cravin-ai/**", "!ai-bot.zip", "!cert-bundle.pem", "!vectors.js", "!vectorized_data.json", "!package-lock.json", "index.mjs", "package.json", "config/**", "handlers/**", "services/**", "utils/**", "node_modules/@aws-sdk/**", "node_modules/pg/**", "node_modules/axios/**", "node_modules/crypto/**"], "artifactsS3KeyDirname": "serverless/cravin-concierge-whatsapp/prod/code-artifacts", "deploymentBucket": "serverless-framework-deployments-us-east-1-6f415178-284f", "artifact": "H:\\Cravin\\Cravin-Concierge\\.serverless\\cravin-concierge-whatsapp.zip", "artifactDirectoryName": "serverless/cravin-concierge-whatsapp/prod/*************-2025-07-22T05:22:32.714Z"}, "plugins": ["serverless-offline"], "custom": {"serverless-offline": {"httpPort": 3000, "host": "localhost", "stage": "local"}, "stages": {"dev": {"memorySize": 512, "timeout": 30}, "prod": {"memorySize": 1024, "timeout": 60}}}, "resources": {"Resources": {"WhatsappWebhookLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/lambda/cravin-concierge-whatsapp-prod-whatsappWebhook", "RetentionInDays": 14}}}, "Outputs": {"WebhookUrl": {"Description": "WhatsApp Webhook URL", "Value": {"Fn::Join": ["", ["https://", {"Ref": "HttpApi"}, ".execute-api.", "us-east-1", ".amazonaws.com/webhook"]]}}, "ServiceName": {"Description": "Service name", "Value": "cravin-concierge-whatsapp"}, "Stage": {"Description": "Deployment stage", "Value": "prod"}}}}, "provider": {"name": "aws", "runtime": "nodejs20.x", "region": "us-east-1", "stage": "prod", "memorySize": 512, "timeout": 30, "httpApi": {"cors": true}, "environment": {"NODE_ENV": "prod", "AWS_REGION": "us-east-1", "WHATSAPP_VERIFY_TOKEN": "<REDACTED>", "WHATSAPP_ACCESS_TOKEN": "<REDACTED>", "WHATSAPP_PHONE_NUMBER_ID": "394218977097517", "WHATSAPP_API_VERSION": "v18.0", "WHATSAPP_WEBHOOK_SECRET": "<REDACTED>", "BEDROCK_MODEL_ID": "meta.llama3-8b-instruct-v1:0", "BEDROCK_AGENT_ID": "R1FMKXHBQF", "BEDROCK_AGENT_ALIAS_ID": "TSTALIASID", "DB_HOST": "ep-throbbing-union-48745705.ap-southeast-1.aws.neon.tech", "DB_PORT": "5432", "DB_NAME": "cravin-booking-demo", "DB_USERNAME": "CravinUAE", "DB_PASSWORD": "<REDACTED>", "DB_SSL": "true", "LOG_LEVEL": "info"}, "iam": {"role": {"statements": [{"Effect": "Allow", "Action": ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream"], "Resource": "arn:aws:bedrock:us-east-1::foundation-model/*"}, {"Effect": "Allow", "Action": ["bedrock:InvokeAgent"], "Resource": ["arn:aws:bedrock:us-east-1:*:agent/*", "arn:aws:bedrock:us-east-1:*:agent-alias/*"]}, {"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": "arn:aws:logs:us-east-1:*:*"}]}}, "versionFunctions": true, "compiledCloudFormationTemplate": {"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"WhatsappWebhookLogGroup": {"Type": "AWS::Logs::LogGroup", "Properties": {"LogGroupName": "/aws/lambda/cravin-concierge-whatsapp-prod-whatsappWebhook", "RetentionInDays": 14}}, "IamRoleLambdaExecution": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": ["lambda.amazonaws.com"]}, "Action": ["sts:<PERSON><PERSON>Role"]}]}, "Policies": [{"PolicyName": {"Fn::Join": ["-", ["cravin-concierge-whatsapp", "prod", "lambda"]]}, "PolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["logs:CreateLogStream", "logs:CreateLogGroup", "logs:TagResource"], "Resource": [{"Fn::Sub": "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/cravin-concierge-whatsapp-prod*:*"}]}, {"Effect": "Allow", "Action": ["logs:PutLogEvents"], "Resource": [{"Fn::Sub": "arn:${AWS::Partition}:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/cravin-concierge-whatsapp-prod*:*:*"}]}, {"Effect": "Allow", "Action": ["bedrock:InvokeModel", "bedrock:InvokeModelWithResponseStream"], "Resource": "arn:aws:bedrock:us-east-1::foundation-model/*"}, {"Effect": "Allow", "Action": ["bedrock:InvokeAgent"], "Resource": ["arn:aws:bedrock:us-east-1:*:agent/*", "arn:aws:bedrock:us-east-1:*:agent-alias/*"]}, {"Effect": "Allow", "Action": ["logs:CreateLogGroup", "logs:CreateLogStream", "logs:PutLogEvents"], "Resource": "arn:aws:logs:us-east-1:*:*"}]}}], "Path": "/", "RoleName": {"Fn::Join": ["-", ["cravin-concierge-whatsapp", "prod", {"Ref": "AWS::Region"}, "lambdaRole"]]}}}, "WhatsappWebhookLambdaFunction": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": "serverless-framework-deployments-us-east-1-6f415178-284f", "S3Key": "serverless/cravin-concierge-whatsapp/prod/*************-2025-07-22T05:22:32.714Z/cravin-concierge-whatsapp.zip"}, "Handler": "index.handler", "Runtime": "nodejs20.x", "FunctionName": "cravin-concierge-whatsapp-prod-whatsappWebhook", "MemorySize": 512, "Timeout": 30, "Description": "Handles WhatsApp webhook verification and incoming messages with AI processing", "Environment": {"Variables": {"NODE_ENV": "prod", "AWS_REGION": "us-east-1", "WHATSAPP_VERIFY_TOKEN": "<REDACTED>", "WHATSAPP_ACCESS_TOKEN": "<REDACTED>", "WHATSAPP_PHONE_NUMBER_ID": "394218977097517", "WHATSAPP_API_VERSION": "v18.0", "WHATSAPP_WEBHOOK_SECRET": "<REDACTED>", "BEDROCK_MODEL_ID": "meta.llama3-8b-instruct-v1:0", "BEDROCK_AGENT_ID": "R1FMKXHBQF", "BEDROCK_AGENT_ALIAS_ID": "TSTALIASID", "DB_HOST": "ep-throbbing-union-48745705.ap-southeast-1.aws.neon.tech", "DB_PORT": "5432", "DB_NAME": "cravin-booking-demo", "DB_USERNAME": "CravinUAE", "DB_PASSWORD": "<REDACTED>", "DB_SSL": "true", "LOG_LEVEL": "info", "FUNCTION_NAME": "whatsappWebhook"}}, "Role": {"Fn::GetAtt": ["IamRoleLambdaExecution", "<PERSON><PERSON>"]}}, "DependsOn": ["WhatsappWebhookLogGroup"]}, "WhatsappWebhookLambdaVersion234BIwXQssP8y2urSmNh6vIaUh0yRNKYdZRsOt09BOE": {"Type": "AWS::Lambda::Version", "DeletionPolicy": "<PERSON><PERSON>", "Properties": {"FunctionName": {"Ref": "WhatsappWebhookLambdaFunction"}, "CodeSha256": "JoU8RJWFOnV4pPiMPfybTnh+XFDmCDt/L/Su6tPJ0FY=", "Description": "Handles WhatsApp webhook verification and incoming messages with AI processing"}}, "HttpApi": {"Type": "AWS::ApiGatewayV2::Api", "Properties": {"Name": "prod-cravin-concierge-whatsapp", "ProtocolType": "HTTP", "CorsConfiguration": {"AllowCredentials": "<REDACTED>", "AllowHeaders": ["Content-Type", "X-Amz-Date", "Authorization", "X-Api-Key", "X-Amz-Security-Token", "X-Amz-User-Agent", "X-Amzn-Trace-Id"], "AllowMethods": ["OPTIONS", "GET", "POST"], "AllowOrigins": ["*"]}}}, "HttpApiStage": {"Type": "AWS::ApiGatewayV2::Stage", "Properties": {"ApiId": {"Ref": "HttpApi"}, "StageName": "$default", "AutoDeploy": true, "DefaultRouteSettings": {"DetailedMetricsEnabled": false}}}, "WhatsappWebhookLambdaPermissionHttpApi": {"Type": "AWS::Lambda::Permission", "Properties": {"FunctionName": {"Fn::GetAtt": ["WhatsappWebhookLambdaFunction", "<PERSON><PERSON>"]}, "Action": "lambda:InvokeFunction", "Principal": "apigateway.amazonaws.com", "SourceArn": {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":execute-api:", {"Ref": "AWS::Region"}, ":", {"Ref": "AWS::AccountId"}, ":", {"Ref": "HttpApi"}, "/*"]]}}}, "HttpApiIntegrationWhatsappWebhook": {"Type": "AWS::ApiGatewayV2::Integration", "Properties": {"ApiId": {"Ref": "HttpApi"}, "IntegrationType": "AWS_PROXY", "IntegrationUri": {"Fn::GetAtt": ["WhatsappWebhookLambdaFunction", "<PERSON><PERSON>"]}, "PayloadFormatVersion": "2.0", "TimeoutInMillis": 30000}}, "HttpApiRouteGetWebhook": {"Type": "AWS::ApiGatewayV2::Route", "Properties": {"ApiId": {"Ref": "HttpApi"}, "RouteKey": "GET /webhook", "Target": {"Fn::Join": ["/", ["integrations", {"Ref": "HttpApiIntegrationWhatsappWebhook"}]]}}, "DependsOn": "HttpApiIntegrationWhatsappWebhook"}, "HttpApiRoutePostWebhook": {"Type": "AWS::ApiGatewayV2::Route", "Properties": {"ApiId": {"Ref": "HttpApi"}, "RouteKey": "POST /webhook", "Target": {"Fn::Join": ["/", ["integrations", {"Ref": "HttpApiIntegrationWhatsappWebhook"}]]}}, "DependsOn": "HttpApiIntegrationWhatsappWebhook"}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": "serverless-framework-deployments-us-east-1-6f415178-284f", "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-ServerlessDeploymentBucketName"}}, "WhatsappWebhookLambdaFunctionQualifiedArn": {"Description": "Current Lambda function version", "Value": {"Ref": "WhatsappWebhookLambdaVersion234BIwXQssP8y2urSmNh6vIaUh0yRNKYdZRsOt09BOE"}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-WhatsappWebhookLambdaFunctionQualifiedArn"}}, "HttpApiId": {"Description": "Id of the HTTP API", "Value": {"Ref": "HttpApi"}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-HttpApiId"}}, "HttpApiUrl": {"Description": "URL of the HTTP API", "Value": {"Fn::Join": ["", ["https://", {"Ref": "HttpApi"}, ".execute-api.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}]]}, "Export": {"Name": "sls-cravin-concierge-whatsapp-prod-HttpApiUrl"}}, "WebhookUrl": {"Description": "WhatsApp Webhook URL", "Value": {"Fn::Join": ["", ["https://", {"Ref": "HttpApi"}, ".execute-api.", "us-east-1", ".amazonaws.com/webhook"]]}}, "ServiceName": {"Description": "Service name", "Value": "cravin-concierge-whatsapp"}, "Stage": {"Description": "Deployment stage", "Value": "prod"}}}, "vpc": {}}, "dashboard": {"isEnabledForService": true, "requiredAuthentication": false, "orgFeaturesInUse": {"providers": false, "monitoring": false}, "orgObservabilityIntegrations": null, "serviceAppId": "dsPX5KlrkGt6bxC6Q1", "serviceProvider": null, "instanceParameters": null}, "error": {"message": "Stack:arn:aws:cloudformation:us-east-1:************:stack/cravin-concierge-whatsapp-prod/ec2bd7f0-66b7-11f0-9530-0e15c688abe7 is in DELETE_FAILED state and can not be updated.", "stack": "ServerlessError2: Stack:arn:aws:cloudformation:us-east-1:************:stack/cravin-concierge-whatsapp-prod/ec2bd7f0-66b7-11f0-9530-0e15c688abe7 is in DELETE_FAILED state and can not be updated.\n    at file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1155:36\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async persistentRequest (file:///C:/Users/<USER>/.serverless/releases/4.17.1/package/dist/sf-core.js:1154:1960)", "code": "AWS_CLOUD_FORMATION_UPDATE_STACK_VALIDATION_ERROR"}, "serviceRawFile": "# Serverless Framework configuration for Cravin Concierge WhatsApp Lambda\n# This service handles WhatsApp webhook verification and message processing with AI capabilities\n\n# Organization and app configuration for Serverless Framework Dashboard\norg: nookeshkarri7\napp: cravin-concierge\nservice: cravin-concierge-whatsapp\n\nprovider:\n  name: aws\n  runtime: nodejs20.x\n  region: ${opt:region, 'us-east-1'}\n  stage: ${opt:stage, 'dev'}\n\n  # Optional: Specify existing S3 bucket for deployments\n  # deploymentBucket:\n  #   name: your-existing-bucket-name\n  \n  # Memory and timeout configuration\n  memorySize: 512\n  timeout: 30\n\n  # HTTP API configuration with CORS\n  httpApi:\n    cors: true\n  \n  # Environment variables\n  environment:\n    NODE_ENV: ${self:provider.stage}\n    AWS_REGION: ${self:provider.region}\n    \n    # WhatsApp Configuration\n    WHATSAPP_VERIFY_TOKEN: ${env:WHATSAPP_VERIFY_TOKEN}\n    WHATSAPP_ACCESS_TOKEN: ${env:WHATSAPP_ACCESS_TOKEN}\n    WHATSAPP_PHONE_NUMBER_ID: ${env:WHATSAPP_PHONE_NUMBER_ID}\n    WHATSAPP_API_VERSION: ${env:WHATSAPP_API_VERSION, 'v18.0'}\n    WHATSAPP_WEBHOOK_SECRET: ${env:WHATSAPP_WEBHOOK_SECRET}\n    \n    # AWS Bedrock Configuration\n    BEDROCK_MODEL_ID: ${env:BEDROCK_MODEL_ID, 'anthropic.claude-3-sonnet-20240229-v1:0'}\n    BEDROCK_AGENT_ID: ${env:BEDROCK_AGENT_ID}\n    BEDROCK_AGENT_ALIAS_ID: ${env:BEDROCK_AGENT_ALIAS_ID, 'TSTALIASID'}\n    \n    # Database Configuration\n    DB_HOST: ${env:DB_HOST}\n    DB_PORT: ${env:DB_PORT, '5432'}\n    DB_NAME: ${env:DB_NAME}\n    DB_USERNAME: ${env:DB_USERNAME}\n    DB_PASSWORD: ${env:DB_PASSWORD}\n    DB_SSL: ${env:DB_SSL, 'true'}\n    \n    # Application Configuration\n    LOG_LEVEL: ${env:LOG_LEVEL, 'info'}\n\n  # IAM permissions for the Lambda function\n  iam:\n    role:\n      statements:\n        # Bedrock permissions for AI processing\n        - Effect: Allow\n          Action:\n            - bedrock:InvokeModel\n            - bedrock:InvokeModelWithResponseStream\n          Resource: \n            - \"arn:aws:bedrock:${self:provider.region}::foundation-model/*\"\n        \n        # Bedrock Agent permissions\n        - Effect: Allow\n          Action:\n            - bedrock:InvokeAgent\n          Resource:\n            - \"arn:aws:bedrock:${self:provider.region}:*:agent/*\"\n            - \"arn:aws:bedrock:${self:provider.region}:*:agent-alias/*\"\n        \n        # CloudWatch Logs permissions\n        - Effect: Allow\n          Action:\n            - logs:CreateLogGroup\n            - logs:CreateLogStream\n            - logs:PutLogEvents\n          Resource: \"arn:aws:logs:${self:provider.region}:*:*\"\n\n  # VPC configuration (uncomment if database is in VPC)\n  # vpc:\n  #   securityGroupIds:\n  #     - ${env:VPC_SECURITY_GROUP_ID}\n  #   subnetIds:\n  #     - ${env:VPC_SUBNET_ID_1}\n  #     - ${env:VPC_SUBNET_ID_2}\n\nfunctions:\n  # Main WhatsApp webhook handler\n  whatsappWebhook:\n    handler: index.handler\n    description: \"Handles WhatsApp webhook verification and incoming messages with AI processing\"\n    events:\n      - httpApi:\n          path: /webhook\n          method: get\n      - httpApi:\n          path: /webhook\n          method: post\n    \n    # Function-specific environment variables (if needed)\n    environment:\n      FUNCTION_NAME: whatsappWebhook\n\n# Package configuration\npackage:\n  # Exclude development files and directories\n  patterns:\n    - '!node_modules/**'\n    - '!.git/**'\n    - '!.env*'\n    - '!README.md'\n    - '!server.mjs'\n    - '!test-webhook.mjs'\n    - '!cravin-ai/**'\n    - '!ai-bot.zip'\n    - '!cert-bundle.pem'\n    - '!vectors.js'\n    - '!vectorized_data.json'\n    - '!package-lock.json'\n    \n    # Include necessary files\n    - 'index.mjs'\n    - 'package.json'\n    - 'config/**'\n    - 'handlers/**'\n    - 'services/**'\n    - 'utils/**'\n    - 'node_modules/@aws-sdk/**'\n    - 'node_modules/pg/**'\n    - 'node_modules/axios/**'\n    - 'node_modules/crypto/**'\n\n# Plugins (optional - install with: npm install --save-dev serverless-offline)\nplugins:\n  - serverless-offline # For local development\n\n# Custom configuration\ncustom:\n  # Serverless Offline configuration for local development\n  serverless-offline:\n    httpPort: 3000\n    host: localhost\n    stage: local\n    \n  # Stage-specific configurations\n  stages:\n    dev:\n      memorySize: 512\n      timeout: 30\n    prod:\n      memorySize: 1024\n      timeout: 60\n\n# Resources (CloudFormation templates)\nresources:\n  Resources:\n    # API Gateway custom domain (optional)\n    # WebhookDomain:\n    #   Type: AWS::ApiGatewayV2::DomainName\n    #   Properties:\n    #     DomainName: ${env:CUSTOM_DOMAIN}\n    #     DomainNameConfigurations:\n    #       - CertificateArn: ${env:SSL_CERTIFICATE_ARN}\n    #         EndpointType: REGIONAL\n    \n    # CloudWatch Log Group with retention\n    WhatsappWebhookLogGroup:\n      Type: AWS::Logs::LogGroup\n      Properties:\n        LogGroupName: /aws/lambda/${self:service}-${self:provider.stage}-whatsappWebhook\n        RetentionInDays: 14\n\n  # Outputs\n  Outputs:\n    WebhookUrl:\n      Description: \"WhatsApp Webhook URL\"\n      Value:\n        Fn::Join:\n          - \"\"\n          - - \"https://\"\n            - Ref: HttpApi\n            - \".execute-api.\"\n            - ${self:provider.region}\n            - \".amazonaws.com/webhook\"\n    \n    ServiceName:\n      Description: \"Service name\"\n      Value: ${self:service}\n    \n    Stage:\n      Description: \"Deployment stage\"\n      Value: ${self:provider.stage}\n", "command": ["deploy"], "options": {"stage": "prod"}, "orgId": "857d34da-486f-464a-bd66-57775f8c6637", "orgName": "nookeshkarri7", "userId": "nn0mGzBw36KF6hQQjM", "userName": "nookeshkarri7", "serviceProviderAwsAccountId": "************", "serviceProviderAwsCfStackName": "cravin-concierge-whatsapp-prod", "serviceProviderAwsCfStackId": "arn:aws:cloudformation:us-east-1:************:stack/cravin-concierge-whatsapp-prod/ec2bd7f0-66b7-11f0-9530-0e15c688abe7", "serviceProviderAwsCfStackCreated": "2025-07-22T04:54:17.721Z", "serviceProviderAwsCfStackUpdated": null, "serviceProviderAwsCfStackStatus": "DELETE_FAILED", "serviceProviderAwsCfStackOutputs": null}}